<?php

namespace App\Models;

use CodeIgniter\Model;

class ExerciseModel extends Model
{
    protected $table         = 'exercises';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $returnType    = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    
    // Fields that are allowed to be set during insert/update operations.
    protected $allowedFields = [
        'org_id',
        'exercise_name',
        'gazzetted_no',
        'gazzetted_date',
        'advertisement_no',
        'advertisement_date',
        'mode_of_advertisement',
        'publish_date_from',
        'publish_date_to',
        'description',
        'pre_screen_criteria',
        'status', //draft, publish_request, published, selection, closed, review
        'created_by',
        'updated_by'
    ];

    // Enable automatic handling of created_at and updated_at fields.
    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $dateFormat    = 'datetime';
    
    // Validation
    protected $validationRules = [
        'org_id'             => 'required|numeric',
        'exercise_name'      => 'required|max_length[255]',
        'gazzetted_no'       => 'required|max_length[255]',
        'gazzetted_date'     => 'required|valid_date',
        'advertisement_no'   => 'required|max_length[255]',
        'advertisement_date' => 'required|valid_date',
        'mode_of_advertisement' => 'required|max_length[100]',
        'publish_date_from'  => 'required|valid_date',
        'publish_date_to'    => 'required|valid_date',
        'status'             => 'required|max_length[20]',
        'created_by'         => 'required|numeric',
        'updated_by'         => 'required|numeric'
    ];
    
    protected $validationMessages = [
        'org_id' => [
            'required' => 'Organization ID is required',
            'numeric'  => 'Organization ID must be a number'
        ],
        'exercise_name' => [
            'required'   => 'Exercise name is required',
            'max_length' => 'Exercise name cannot exceed 255 characters'
        ]
    ];
    
    protected $skipValidation = false;
    protected $cleanValidationRules = true;
    
    /**
     * Get exercises by organization ID
     *
     * @param int $orgId
     * @return array
     */
    public function getExercisesByOrgId($orgId)
    {
        return $this->where('org_id', $orgId)->findAll();
    }
    
    /**
     * Get active exercises (published status)
     *
     * @return array
     */
    public function getActiveExercises()
    {
        return $this->where('status', 'publish')
                    ->where('publish_date_from <=', date('Y-m-d'))
                    ->where('publish_date_to >=', date('Y-m-d'))
                    ->findAll();
    }
    
    /**
     * Get draft exercises
     *
     * @param int $orgId
     * @return array
     */
    public function getDraftExercises($orgId = null)
    {
        $builder = $this->where('status', 'draft');
        
        if ($orgId !== null) {
            $builder->where('org_id', $orgId);
        }
        
        return $builder->findAll();
    }
} 