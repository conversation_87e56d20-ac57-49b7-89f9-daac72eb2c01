<?php namespace App\Models;

use CodeIgniter\Model;

class applicantsModel extends Model
{
    protected $table = 'applicants';
    protected $primaryKey = 'applicant_id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;


    protected $allowedFields = [
        'unique_id',
        'email',
        'password',
        'fname',
        'lname',
        'gender',
        'dobirth',
        'place_of_origin',
        'id_photo_path',
        'contact_details',
        'location_address',
        'id_numbers', //nid, passport, driving license etc...
        'current_employer',
        'current_position',
        'current_salary',
        'citizenship',
        'marital_status', //single, married, divorced, widowed, separated
        'date_of_marriage', //if married
        'spouse_employer', //if married - spouse's employer name or public servant file number if government employee
        'children', //name, date of birth, gender
        'offence_convicted', //write details if convicted leave blank if not convicted
        'referees', //name, address, contact details
        'how_did_you_hear_about_us', //from newspaper, social media, website, etc.
        'signature_path',
        'publications', //name and date of the publications if any
        'awards', //name and date of the awards if any
        'status', // active, inactive, pending
        'activation_token',
        'created_by',
        'updated_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Callbacks
    protected $beforeInsert = ['hashPassword'];
    protected $beforeUpdate = ['hashPassword'];

    protected function hashPassword(array $data)
    {
        if (!isset($data['data']['password'])) {
            return $data;
        }

        $data['data']['password'] = password_hash($data['data']['password'], PASSWORD_DEFAULT);
        return $data;
    }
}