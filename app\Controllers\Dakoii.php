<?php

namespace App\Controllers;

use App\Models\countryModel;
use App\Models\dakoiiUsersModel;
use App\Models\orgModel;
use App\Models\provinceModel;
use App\Models\usersModel;
use App\Models\selectionModel;

class Dakoii extends BaseController
{
    public $session;
    public $dusersModel;
    public $usersModel;
    public $orgModel;
    public $countryModel;
    public $provinceModel;
    public $districtModel;
    public $llgModel;
    public $wardModel;
    public $selectionModel;

    public $educationModel;


    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        $this->dusersModel = new dakoiiUsersModel();
        $this->usersModel = new usersModel();
        $this->orgModel = new orgModel();
        $this->countryModel = new countryModel();
        $this->provinceModel = new provinceModel();
        $this->districtModel = new \App\Models\districtModel();
        $this->llgModel = new \App\Models\llgModel();
        $this->wardModel = new \App\Models\wardModel();
        $this->selectionModel = new selectionModel();

        $this->educationModel = new \App\Models\EducationModel();

    }

    // Authentication Methods
    public function index()
    {
        $data['title'] = "Dakoii Admin";
        $data['menu'] = "dlogin";
        echo view('dakoii/dakoii_login', $data);
    }

    public function adminLogin()
    {
        if ($this->request->getMethod() == 'post') {
            $rules = [
                'username' => 'required',
                'password' => 'required'
            ];
            if (!$this->validate($rules)) {
                session()->setFlashdata('error', 'Please enter both username and password');
                return redirect()->to('dakoii');
            }

            $username = $this->request->getVar('username');
            $password = $this->request->getVar('password');

            $user = $this->dusersModel->where('username', $username)->first();
            if (!$user || !password_verify($password, $user['password'])) {
                session()->setFlashdata('error', 'Invalid username or password');
                return redirect()->to('dakoii');
            }

            $this->session->set([
                'logged_in' => true,
                'name' => $user['name'],
                'username' => $user['username'],
                'role' => $user['role'],
                'user_id' => $user['id']
            ]);

            session()->setFlashdata('success', 'Welcome back, ' . $user['name']);
            return redirect()->to('dakoii/dashboard');
        }

        session()->setFlashdata('error', 'Error logging in');
        return redirect()->to('dakoii');
    }

    public function adminLogout()
    {
        $session = session();
        $session->destroy();
        return redirect()->to(base_url());
    }

    // Dashboard Methods
    public function dashboard()
    {
        $data['title'] = "Dashboard";
        $data['menu'] = "dashboard";

        $data['dusers'] = $this->dusersModel->findAll();
        $data['admins'] = $this->usersModel->findAll();
        $data['org'] = $this->orgModel->orderBy('id', 'DESC')->findAll();
        $data['selections'] = $this->selectionModel->orderBy('id', 'DESC')->findAll();

        $data['provinces_count'] = $this->provinceModel->countAllResults();
        $data['districts_count'] = $this->districtModel->countAllResults();
        $data['llgs_count'] = $this->llgModel->countAllResults();
        $data['wards_count'] = $this->wardModel->countAllResults();

        $data['province_stats'] = $this->getProvinceStats();


        $data['education'] = $this->educationModel->findAll();

        // Get pending exercises with publish_request status
        $exerciseModel = new \App\Models\ExerciseModel();
        $data['pending_exercises'] = $exerciseModel->where('status', 'publish_request')->findAll();

        // If we have pending exercises, let's get their organization names
        if (!empty($data['pending_exercises'])) {
            foreach ($data['pending_exercises'] as &$exercise) {
                if (!empty($exercise['org_id'])) {
                    $org = $this->orgModel->find($exercise['org_id']);
                    if ($org) {
                        // Use the orgModel's method to get the organization name
                        $exercise['org_name'] = $this->orgModel->getOrganizationName($org);
                    }
                }
            }
        }

        echo view('dakoii/dakoii_ddash', $data);
    }

    private function getProvinceStats()
    {
        $provinces = $this->provinceModel->findAll();
        $stats = [];

        foreach ($provinces as $province) {
            $districts = $this->districtModel->where('province_id', $province['id'])->findAll();
            $district_ids = array_column($districts, 'id');

            $llgs_count = 0;
            $wards_count = 0;

            if (!empty($district_ids)) {
                $llgs_count = $this->llgModel->whereIn('district_id', $district_ids)->countAllResults();
                $llgs = $this->llgModel->whereIn('district_id', $district_ids)->findAll();
                $llg_ids = array_column($llgs, 'id');

                if (!empty($llg_ids)) {
                    $wards_count = $this->wardModel->whereIn('llg_id', $llg_ids)->countAllResults();
                }
            }

            $stats[$province['id']] = [
                'name' => $province['name'],
                'districts' => count($districts),
                'llgs' => $llgs_count,
                'wards' => $wards_count
            ];
        }

        return $stats;
    }

    // Organization Methods
    public function organizationList()
    {
        $data['title'] = "Organizations";
        $data['menu'] = "organizations";
        $data['organizations'] = $this->orgModel->findAll();
        echo view('dakoii/dakoii_organizations', $data);
    }

    public function organizationCreate()
    {
        if ($this->request->getMethod() === 'post' && $this->validate(['org_name' => 'required'])) {
            $orgcode = rand(11111, 99999);
            if (!empty($this->orgModel->where('org_code', $orgcode)->first())) {
                $orgcode = rand(11111, 99999);
            }

            $data = [
                'org_code' => $orgcode,
                'org_name' => $this->request->getVar('org_name'),
                'description' => $this->request->getVar('description'),
                'is_active' => 1,
            ];

            $this->orgModel->insert($data);

            $logoFile = $this->request->getFile('org_logo');
            if ($logoFile->isValid() && $logoFile->getSize() > 0) {
                // Make sure the upload directory exists
                $uploadPath = ROOTPATH . 'public/uploads/org_logo/';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0777, true);
                }

                $newName = $orgcode . "_" . time() . '.' . $logoFile->getExtension();
                $logoFile->move($uploadPath, $newName);

                // Store only the relative path without base_url
                $data['orglogo'] = 'public/uploads/org_logo/' . $newName;

                $getid = $this->orgModel->where('orgcode', $orgcode)->first();
                $this->orgModel->update($getid['id'], $data);

                // Log the upload for debugging
                log_message('info', 'Organization logo uploaded: ' . $data['orglogo']);
            }

            session()->setFlashdata('success', 'Organization Created');
            return redirect()->to(base_url('dakoii/organization/view/' . $orgcode));
        }

        session()->setFlashdata('success', 'Enter valid Data');
        return redirect()->to('dakoii/dashboard');
    }

    public function organizationView($orgcode)
    {
        $org = $this->orgModel->where('org_code', $orgcode)->first();
        if (empty($org)) {
            return redirect()->to(base_url('dakoii/dashboard'));
        }

        $data['title'] = "Organization Details";
        $data['menu'] = "organizations";
        $data['org'] = $org;
        $data['admins'] = $this->usersModel->where('orgcode', $orgcode)->findAll();

        // Get the province and country
        if (!empty($org['location_lock_country'])) {
            $country = $this->countryModel->find($org['location_lock_country']);
            if (!empty($country)) {
                $data['country_name'] = $country['name'];
            }
        }

        if (!empty($org['location_lock_province'])) {
            $province = $this->provinceModel->find($org['location_lock_province']);
            if (!empty($province)) {
                $data['province_name'] = $province['name'];
            }
        }

        // Get all provinces for the set country
        $data['set_country'] = $this->countryModel->findAll()[0];
        $data['get_provinces'] = $this->provinceModel->findAll();

        // Get exercises for this organization
        $exerciseModel = new \App\Models\ExerciseModel();
        $data['exercises'] = $exerciseModel->where('org_id', $org['id'])->findAll();

        // Check if the user has permission to add exercises
        $data['can_add_exercise'] = true; // Default to true, adjust based on your permission system

        echo view('dakoii/dakoii_open_org', $data);
    }

    public function organizationUpdate()
    {
        if ($this->request->getMethod() === 'post' && $this->validate(['org_name' => 'required'])) {
            $id = $this->request->getVar('id');
            $orgcode = $this->request->getVar('orgcode');

            $addprov = "";
            if (!empty($this->request->getVar('country'))) {
                $addprov = $this->request->getVar('province');
            }

            $data = [
                'org_name' => $this->request->getVar('org_name'),
                'description' => $this->request->getVar('description'),
                'location_lock_country' => $this->request->getVar('country'),
                'location_lock_province' => $addprov,
                'is_active' => $this->request->getVar('status'),
            ];

            // Process logo upload
            $logoFile = $this->request->getFile('org_logo');
            if ($logoFile && $logoFile->isValid() && $logoFile->getSize() > 0) {
                // Make sure the upload directory exists
                $uploadPath = ROOTPATH . 'public/uploads/org_logo/';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0777, true);
                }

                // Generate unique name and move the file
                $newName = $orgcode . "_" . time() . '.' . $logoFile->getExtension();
                $logoFile->move($uploadPath, $newName);

                // Set the relative path for storage
                $data['logo_path'] = 'public/uploads/org_logo/' . $newName;

                // Log the upload for debugging
                log_message('info', 'Organization logo uploaded: ' . $data['logo_path']);
            }

            $this->orgModel->update($id, $data);
            session()->setFlashdata('success', 'Organization Updated Successfully');
        }
        return redirect()->back();
    }

    public function organizationUpdateLicense()
    {
        $id = $this->request->getVar('id');
        $orgcode = $this->request->getVar('orgcode');

        $data = [
            'license_status' => $this->request->getVar('license_status'),
        ];

        $this->orgModel->update($id, $data);

        session()->setFlashdata('success', 'License Status Changed');
        return redirect()->to('dakoii/organization/view/'.$orgcode);
    }

    // User Management Methods
    public function systemUserCreate()
    {
        if ($this->request->getMethod() === 'post' && $this->validate([
            'username' => 'required|is_unique[dakoii_users.username]',
            'password' => 'required'
        ])) {
            $is_active = !empty($this->request->getVar('is_active')) ? $this->request->getVar('is_active') : "0";

            $data = [
                'name' => $this->request->getVar('name'),
                'username' => $this->request->getVar('username'),
                'password' => password_hash($this->request->getVar('password'), PASSWORD_DEFAULT),
                'role' => $this->request->getVar('role'),
                'is_active' => $is_active,
            ];

            $this->dusersModel->insert($data);
            session()->setFlashdata('success', 'Admin Created');
            return redirect()->to('dakoii/dashboard');
        }

        session()->setFlashdata('error', 'Username already exist');
        return redirect()->to('dakoii/dashboard');
    }

    public function organizationAdminCreate()
    {
        $orgcode = $this->request->getVar('orgcode');
        if ($this->request->getMethod() === 'post' && $this->validate([
            'username' => 'required|is_unique[users.username]',
            'password' => 'required'
        ])) {
            $status = !empty($this->request->getVar('is_active')) ? 1 : 0;

            // Get organization details
            $org = $this->orgModel->where('org_code', $orgcode)->first();
            if (!$org) {
                session()->setFlashdata('error', 'Invalid organization');
                return redirect()->back();
            }

            $data = [
                'org_id' => $org['id'], // Set org_id from the organization
                'orgcode' => $orgcode,
                'name' => $this->request->getVar('name'),
                'username' => $this->request->getVar('username'),
                'password' => $this->request->getVar('password'),
                'role' => $this->request->getVar('role'),
                'status' => $status,
                'created_by' => session()->get('user_id'), // Add created_by from session
                'updated_by' => session()->get('user_id')  // Add updated_by from session
            ];

            $this->usersModel->insert($data);
            session()->setFlashdata('success', 'Organization Admin Created');
            return redirect()->to('dakoii/organization/view/'.$orgcode);
        }

        session()->setFlashdata('error', 'Username already taken');
        return redirect()->to('dakoii/organization/view/'.$orgcode);
    }

    public function organizationAdminUpdate()
    {
        if ($this->request->getMethod() === 'post') {
            $id = $this->request->getVar('id');
            $orgcode = $this->request->getVar('orgcode');

            // Get organization details
            $org = $this->orgModel->where('org_code', $orgcode)->first();
            if (!$org) {
                session()->setFlashdata('error', 'Invalid organization');
                return redirect()->back();
            }

            $rules = [
                'name' => 'required',
                'username' => 'required|is_unique[users.username,id,' . $id . ']',
                'role' => 'required'
            ];

            if ($this->validate($rules)) {
                $data = [
                    'org_id' => $org['id'], // Set org_id from the organization
                    'name' => $this->request->getVar('name'),
                    'username' => $this->request->getVar('username'),
                    'role' => $this->request->getVar('role'),
                    'status' => $this->request->getVar('is_active') ? 1 : 0,
                    'updated_by' => session()->get('user_id')  // Add updated_by from session
                ];

                // Handle password update
                $password = $this->request->getVar('password');
                if (!empty($password)) {
                    $data['password'] = $password;
                }

                if ($this->usersModel->update($id, $data)) {
                    session()->setFlashdata('success', 'Admin updated successfully');
                } else {
                    session()->setFlashdata('error', 'Failed to update admin');
                }
            } else {
                session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            }
            return redirect()->back();
        }

        session()->setFlashdata('error', 'Invalid request');
        return redirect()->back();
    }

    public function systemUserUpdate()
    {
        if ($this->request->getMethod() === 'post') {
            $id = $this->request->getPost('id');

            $rules = [
                'name' => 'required',
                'username' => 'required|is_unique[dakoii_users.username,id,' . $id . ']',
                'role' => 'required'
            ];

            if ($this->validate($rules)) {
                $data = [
                    'name' => $this->request->getPost('name'),
                    'username' => $this->request->getPost('username'),
                    'role' => $this->request->getPost('role'),
                    'is_active' => $this->request->getPost('is_active') ? 1 : 0
                ];

                if (!empty($this->request->getPost('password'))) {
                    $data['password'] = password_hash($this->request->getPost('password'), PASSWORD_DEFAULT);
                }

                if ($this->dusersModel->update($id, $data)) {
                    session()->setFlashdata('success', 'System user updated successfully');
                } else {
                    session()->setFlashdata('error', 'Failed to update system user');
                }
            } else {
                session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            }
        }
        return redirect()->to('dakoii/dashboard');
    }

    // Province Management Methods
    public function provinceList()
    {
        $data['title'] = "Provinces";
        $data['menu'] = "provinces";

        $data['set_country'] = $this->countryModel->where('code', COUNTRY_CODE)->first();
        $data['provinces'] = $this->provinceModel
            ->where('country_id', $data['set_country']['id'])
            ->orderBy('name', 'asc')
            ->findAll();

        echo view('dakoii/dakoii_provinces', $data);
    }

    public function provinceCreate()
    {
        if ($this->request->getMethod() === 'post') {
            $rules = [
                'name' => 'required|min_length[3]|max_length[100]',
                'provincecode' => 'required|is_unique[adx_province.provincecode]'
            ];

            if ($this->validate($rules)) {
                $data = [
                    'name' => $this->request->getPost('name'),
                    'provincecode' => $this->request->getPost('provincecode'),
                    'country_id' => $this->request->getPost('country_id'),
                    'json_id' => $this->request->getPost('json_id')
                ];

                if ($this->provinceModel->insert($data)) {
                    session()->setFlashdata('success', 'Province "' . $data['name'] . '" has been added successfully');
                } else {
                    session()->setFlashdata('error', 'Failed to add province. Please try again.');
                }
            } else {
                session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            }
        }
        return redirect()->to('dakoii/province/list');
    }

    public function provinceUpdate()
    {
        if ($this->request->getMethod() === 'post') {
            $id = $this->request->getPost('id');

            $rules = [
                'name' => 'required|min_length[3]|max_length[100]',
                'provincecode' => 'required|is_unique[adx_province.provincecode,id,' . $id . ']'
            ];

            if ($this->validate($rules)) {
                $data = [
                    'name' => $this->request->getPost('name'),
                    'provincecode' => $this->request->getPost('provincecode'),
                    'json_id' => $this->request->getPost('json_id')
                ];

                if ($this->provinceModel->update($id, $data)) {
                    session()->setFlashdata('success', 'Province updated successfully');
                } else {
                    session()->setFlashdata('error', 'Failed to update province');
                }
            } else {
                session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            }
        }
        return redirect()->to('dakoii/province/list');
    }

    public function provinceDelete($id)
    {
        if ($this->provinceModel->delete($id)) {
            session()->setFlashdata('success', 'Province deleted successfully');
        } else {
            session()->setFlashdata('error', 'Failed to delete province');
        }
        return redirect()->to('dakoii/province/list');
    }

    public function provinceGet($id)
    {
        $province = $this->provinceModel->find($id);
        return $this->response->setJSON($province);
    }

    // District Management Methods
    public function districtList($provinceId)
    {
        $data['title'] = "Districts";
        $data['menu'] = "provinces";

        $data['province'] = $this->provinceModel->find($provinceId);
        if (empty($data['province'])) {
            return redirect()->to('dakoii/provinces');
        }

        $data['districts'] = $this->districtModel
            ->where('province_id', $provinceId)
            ->orderBy('name', 'asc')
            ->findAll();

        echo view('dakoii/dakoii_districts', $data);
    }

    public function districtGetByProvince($provinceId)
    {
        $districts = $this->districtModel
            ->where('province_id', $provinceId)
            ->orderBy('name', 'asc')
            ->findAll();

        return $this->response->setJSON($districts);
    }

    public function districtCreate()
    {
        if ($this->request->getMethod() === 'post') {
            $rules = [
                'name' => 'required|min_length[3]|max_length[100]',
                'districtcode' => 'required|is_unique[adx_district.districtcode]',
                'province_id' => 'required|numeric',
                'country_id' => 'required|numeric'
            ];

            if ($this->validate($rules)) {
                $data = [
                    'name' => $this->request->getPost('name'),
                    'districtcode' => $this->request->getPost('districtcode'),
                    'province_id' => $this->request->getPost('province_id'),
                    'country_id' => $this->request->getPost('country_id'),
                    'json_id' => $this->request->getPost('json_id')
                ];

                if ($this->districtModel->insert($data)) {
                    session()->setFlashdata('success', 'District "' . $data['name'] . '" has been added successfully');
                } else {
                    session()->setFlashdata('error', 'Failed to add district. Please try again.');
                }
            } else {
                session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            }
        }
        return redirect()->back();
    }

    public function districtUpdate()
    {
        if ($this->request->getMethod() === 'post') {
            $id = $this->request->getPost('id');

            $rules = [
                'name' => 'required|min_length[3]|max_length[100]',
                'districtcode' => 'required|is_unique[adx_district.districtcode,id,' . $id . ']'
            ];

            if ($this->validate($rules)) {
                $data = [
                    'name' => $this->request->getPost('name'),
                    'districtcode' => $this->request->getPost('districtcode'),
                    'json_id' => $this->request->getPost('json_id')
                ];

                if ($this->districtModel->update($id, $data)) {
                    session()->setFlashdata('success', 'District updated successfully');
                } else {
                    session()->setFlashdata('error', 'Failed to update district');
                }
            } else {
                session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            }
        }
        return redirect()->back();
    }

    public function districtDelete($id)
    {
        $district = $this->districtModel->find($id);
        if ($district) {
            if ($this->districtModel->delete($id)) {
                session()->setFlashdata('success', 'District "' . $district['name'] . '" has been deleted successfully');
            } else {
                session()->setFlashdata('error', 'Failed to delete district. Please try again.');
            }
        } else {
            session()->setFlashdata('error', 'District not found');
        }
        return redirect()->back();
    }

    // LLG Management Methods
    public function llgList($districtId)
    {
        $data['title'] = "Local Level Governments";
        $data['menu'] = "provinces";

        $data['district'] = $this->districtModel->find($districtId);
        if (empty($data['district'])) {
            return redirect()->to('dakoii/provinces');
        }

        $data['province'] = $this->provinceModel->find($data['district']['province_id']);

        $data['llgs'] = $this->llgModel
            ->where('district_id', $districtId)
            ->orderBy('name', 'asc')
            ->findAll();

        echo view('dakoii/dakoii_llgs', $data);
    }

    public function llgCreate()
    {
        if ($this->request->getMethod() === 'post') {
            $rules = [
                'name' => 'required|min_length[3]|max_length[100]',
                'llgcode' => 'required|is_unique[adx_llg.llgcode]',
                'district_id' => 'required|numeric',
                'province_id' => 'required|numeric',
                'country_id' => 'required|numeric'
            ];

            if ($this->validate($rules)) {
                $data = [
                    'name' => $this->request->getPost('name'),
                    'llgcode' => $this->request->getPost('llgcode'),
                    'district_id' => $this->request->getPost('district_id'),
                    'province_id' => $this->request->getPost('province_id'),
                    'country_id' => $this->request->getPost('country_id'),
                    'json_id' => $this->request->getPost('json_id')
                ];

                if ($this->llgModel->insert($data)) {
                    session()->setFlashdata('success', 'LLG "' . $data['name'] . '" has been added successfully');
                } else {
                    session()->setFlashdata('error', 'Failed to add LLG. Please try again.');
                }
            } else {
                session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            }
        }
        return redirect()->back();
    }

    public function llgUpdate()
    {
        if ($this->request->getMethod() === 'post') {
            $id = $this->request->getPost('id');

            $rules = [
                'name' => 'required|min_length[3]|max_length[100]',
                'llgcode' => 'required|is_unique[adx_llg.llgcode,id,' . $id . ']'
            ];

            if ($this->validate($rules)) {
                $data = [
                    'name' => $this->request->getPost('name'),
                    'llgcode' => $this->request->getPost('llgcode'),
                    'json_id' => $this->request->getPost('json_id')
                ];

                if ($this->llgModel->update($id, $data)) {
                    session()->setFlashdata('success', 'LLG updated successfully');
                } else {
                    session()->setFlashdata('error', 'Failed to update LLG');
                }
            } else {
                session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            }
        }
        return redirect()->back();
    }

    public function llgDelete($id)
    {
        $llg = $this->llgModel->find($id);
        if ($llg) {
            if ($this->llgModel->delete($id)) {
                session()->setFlashdata('success', 'LLG "' . $llg['name'] . '" has been deleted successfully');
            } else {
                session()->setFlashdata('error', 'Failed to delete LLG. Please try again.');
            }
        } else {
            session()->setFlashdata('error', 'LLG not found');
        }
        return redirect()->back();
    }

    // Ward Management Methods
    public function wardList($llgId)
    {
        $data['title'] = "Wards";
        $data['menu'] = "provinces";

        $data['llg'] = $this->llgModel->find($llgId);
        if (empty($data['llg'])) {
            return redirect()->to('dakoii/provinces');
        }

        $data['district'] = $this->districtModel->find($data['llg']['district_id']);
        $data['province'] = $this->provinceModel->find($data['district']['province_id']);

        $data['wards'] = $this->wardModel
            ->where('llg_id', $llgId)
            ->orderBy('name', 'asc')
            ->findAll();

        echo view('dakoii/dakoii_wards', $data);
    }

    public function wardCreate()
    {
        if ($this->request->getMethod() === 'post') {
            $rules = [
                'name' => 'required|min_length[3]|max_length[100]',
                'wardcode' => 'required|is_unique[adx_ward.wardcode]',
                'llg_id' => 'required|numeric',
                'district_id' => 'required|numeric',
                'province_id' => 'required|numeric',
                'country_id' => 'required|numeric'
            ];

            if ($this->validate($rules)) {
                $data = [
                    'name' => $this->request->getPost('name'),
                    'wardcode' => $this->request->getPost('wardcode'),
                    'llg_id' => $this->request->getPost('llg_id'),
                    'district_id' => $this->request->getPost('district_id'),
                    'province_id' => $this->request->getPost('province_id'),
                    'country_id' => $this->request->getPost('country_id')
                ];

                if ($this->wardModel->insert($data)) {
                    session()->setFlashdata('success', 'Ward "' . $data['name'] . '" has been added successfully');
                } else {
                    session()->setFlashdata('error', 'Failed to add ward. Please try again.');
                }
            } else {
                session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            }
        }
        return redirect()->back();
    }

    public function wardUpdate()
    {
        if ($this->request->getMethod() === 'post') {
            $id = $this->request->getPost('id');

            $rules = [
                'name' => 'required|min_length[3]|max_length[100]',
                'wardcode' => 'required|is_unique[adx_ward.wardcode,id,' . $id . ']'
            ];

            if ($this->validate($rules)) {
                $data = [
                    'name' => $this->request->getPost('name'),
                    'wardcode' => $this->request->getPost('wardcode')
                ];

                if ($this->wardModel->update($id, $data)) {
                    session()->setFlashdata('success', 'Ward updated successfully');
                } else {
                    session()->setFlashdata('error', 'Failed to update ward');
                }
            } else {
                session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            }
        }
        return redirect()->back();
    }

    public function wardDelete($id)
    {
        $ward = $this->wardModel->find($id);
        if ($ward) {
            if ($this->wardModel->delete($id)) {
                session()->setFlashdata('success', 'Ward "' . $ward['name'] . '" has been deleted successfully');
            } else {
                session()->setFlashdata('error', 'Failed to delete ward. Please try again.');
            }
        } else {
            session()->setFlashdata('error', 'Ward not found');
        }
        return redirect()->back();
    }

    // Education
    public function educationCreate()
    {
        if ($this->request->getMethod() === 'post') {
            $data = [
                'name' => $this->request->getPost('name'),
                'icon' => $this->request->getPost('icon'),
                'color_code' => $this->request->getPost('color_code'),
                'remarks' => $this->request->getPost('remarks'),
                'created_by' => session()->get('user_id')
            ];

            if ($this->educationModel->insert($data)) {
                session()->setFlashdata('success', 'Education item added successfully');
            } else {
                session()->setFlashdata('error', 'Failed to add education item');
            }
        }
        return redirect()->to('dakoii/dashboard');
    }

    public function educationUpdate()
    {
        if ($this->request->getMethod() === 'post') {
            $id = $this->request->getPost('id');
            $data = [
                'name' => $this->request->getPost('name'),
                'icon' => $this->request->getPost('icon'),
                'color_code' => $this->request->getPost('color_code'),
                'remarks' => $this->request->getPost('remarks'),
                'updated_by' => session()->get('user_id')
            ];

            if ($this->educationModel->update($id, $data)) {
                session()->setFlashdata('success', 'Education item updated successfully');
            } else {
                session()->setFlashdata('error', 'Failed to update education item');
            }
        }
        return redirect()->to('dakoii/dashboard');
    }

    /**
     * Change the status of an exercise
     *
     * @param int $exerciseId The ID of the exercise to update
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function exerciseChangeStatus($exerciseId)
    {
        if ($this->request->getMethod() === 'post') {
            $status = $this->request->getVar('status');

            // Validate the status
            $validStatuses = ['publish', 'draft', 'selection', 'review', 'publish_request', 'closed'];
            if (!in_array($status, $validStatuses)) {
                session()->setFlashdata('error', 'Invalid status value');
                return redirect()->to(base_url('dakoii/dashboard'));
            }

            // Load the ExerciseModel
            $exerciseModel = new \App\Models\ExerciseModel();

            // Get the exercise
            $exercise = $exerciseModel->find($exerciseId);
            if (!$exercise) {
                session()->setFlashdata('error', 'Exercise not found');
                return redirect()->to(base_url('dakoii/dashboard'));
            }

            // Update the exercise status
            $exerciseModel->update($exerciseId, [
                'status' => $status,
                'updated_by' => session()->get('user_id')
            ]);

            session()->setFlashdata('success', 'Exercise status updated successfully to ' . ucfirst(str_replace('_', ' ', $status)));

            // Check if there's a redirect parameter
            $redirect = $this->request->getGet('redirect');
            if ($redirect && strpos($redirect, 'org/') === 0) {
                $orgcode = substr($redirect, 4);
                return redirect()->to(base_url('dakoii/organization/view/' . $orgcode));
            }
        }

        return redirect()->to(base_url('dakoii/dashboard'));
    }

}
